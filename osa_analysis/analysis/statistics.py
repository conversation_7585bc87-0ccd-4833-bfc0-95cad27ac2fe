"""
Statistics Module
----------------
Generates statistical analyses for OSA data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import chi2_contingency, ttest_ind, pearsonr
import os

class StatisticsGenerator:
    """Generates statistical analyses for OSA data."""

    def generate_statistics(self, df, top_osa_meds, top_osa_atc, atc_classes):
        """Generate statistics and print results.

        Args:
            df (DataFrame): The merged dataset
            top_osa_meds (Series): Top medications for OSA patients
            top_osa_atc (Series): Top ATC codes for OSA patients
            atc_classes (dict): Dictionary of ATC classes
        """
        print("\n" + "="*50)
        print("OBSTRUCTIVE SLEEP APNEA (G47.31) ANALYSIS")
        print("="*50)

        self._basic_statistics(df)
        self._suspected_osa_analysis(df)
        self._demographic_analysis(df)
        self._hypertension_analysis(df)
        self._medication_analysis(df, atc_classes)
        self._comorbidity_analysis(df)
        self._diagnosis_analysis(df)
        self._correlation_analysis(df)
        self._medication_patterns(top_osa_meds, top_osa_atc)

    def _basic_statistics(self, df):
        """Generate basic statistics about OSA and sleep studies."""
        # Basic statistics
        total_patients = len(df)
        osa_patients = df['has_osa'].sum()

        # In the original stats.py, sleep study patients are those in the dataframe
        # since the dataframe is built from sleep study patients
        sleep_study_patients = total_patients

        print(f"\nTotal patients in database: {total_patients}")
        print(f"Patients with OSA (G47.31): {osa_patients} ({osa_patients/total_patients*100:.2f}%)")
        print(f"Patients who underwent sleep study (OPS 1-790): {sleep_study_patients} ({sleep_study_patients/total_patients*100:.2f}%)")

        # Relationship between sleep study and OSA diagnosis
        sleep_study_with_osa = df[df['has_osa'] == 1].shape[0]
        sleep_study_with_suspected_osa = df[(df['has_suspected_osa'] == 1)].shape[0]
        sleep_study_with_unsuspected_osa = df[(df['has_unsuspected_osa'] == 1)].shape[0]
        suspectedTP = df[(df['has_suspected_osa'] == 1) & (df['has_osa'] == 1)].shape[0]
        print(f"Patients with both OSA and sleep study: {sleep_study_with_osa}")
        print(f"Percentage of OSA patients who had sleep study: {sleep_study_with_osa/osa_patients*100:.2f}% (if not 100%, there may be data issues)")
        print(f"Percentage of sleep study patients diagnosed with OSA: {sleep_study_with_osa/sleep_study_patients*100:.2f}%")
        print(f"Percentage of sleep study with suspected OSA: {sleep_study_with_suspected_osa/sleep_study_patients*100:.2f}%")
        print(f"Percentage of sleep study with un suspected OSA: {sleep_study_with_unsuspected_osa/sleep_study_patients*100:.2f}%")
        print(f"Percentage of sleep study with suspected OSA and OSA: {suspectedTP/len(df['has_osa'])*100:.2f}%")
        pass

    def _suspected_osa_analysis(self, df):
        """Analyze suspected OSA patterns and sex differences."""
        print("\n" + "-"*50)
        print("SUSPECTED OSA ANALYSIS")
        print("-"*50)

        total_patients = len(df)
        suspected_osa_patients = df['has_suspected_osa'].sum()
        confirmed_osa_patients = df['has_osa'].sum()

        print(f"\nOverall suspected OSA statistics:")
        print(f"Total patients with suspected OSA: {suspected_osa_patients} ({suspected_osa_patients/total_patients*100:.2f}%)")
        print(f"Total patients with confirmed OSA: {confirmed_osa_patients} ({confirmed_osa_patients/total_patients*100:.2f}%)")

        # Suspected OSA that became confirmed OSA
        suspected_and_confirmed = df[(df['has_suspected_osa'] == 1) & (df['has_osa'] == 1)].shape[0]
        suspected_not_confirmed = df[(df['has_suspected_osa'] == 1) & (df['has_osa'] == 0)].shape[0]
        
        print(f"\nSuspected OSA outcomes:")
        print(f"Suspected OSA confirmed by diagnosis: {suspected_and_confirmed} ({suspected_and_confirmed/suspected_osa_patients*100:.2f}% of suspected cases)")
        print(f"Suspected OSA not confirmed: {suspected_not_confirmed} ({suspected_not_confirmed/suspected_osa_patients*100:.2f}% of suspected cases)")

        # Positive predictive value of suspected OSA
        if suspected_osa_patients > 0:
            ppv = suspected_and_confirmed / suspected_osa_patients * 100
            print(f"Positive predictive value of suspected OSA: {ppv:.2f}%")

        # Sex distribution analysis for suspected OSA
        if 'Sex' in df.columns and df['Sex'].notna().any():
            print(f"\nSuspected OSA by sex:")
            
            for sex in df['Sex'].unique():
                if pd.notna(sex):
                    sex_total = df[df['Sex'] == sex].shape[0]
                    sex_suspected = df[(df['Sex'] == sex) & (df['has_suspected_osa'] == 1)].shape[0]
                    sex_confirmed = df[(df['Sex'] == sex) & (df['has_osa'] == 1)].shape[0]
                    sex_suspected_and_confirmed = df[(df['Sex'] == sex) & (df['has_suspected_osa'] == 1) & (df['has_osa'] == 1)].shape[0]
                    
                    print(f"\n  {sex} patients (n={sex_total}):")
                    print(f"    Suspected OSA: {sex_suspected} ({sex_suspected/sex_total*100:.2f}%)")
                    print(f"    Confirmed OSA: {sex_confirmed} ({sex_confirmed/sex_total*100:.2f}%)")
                    
                    if sex_suspected > 0:
                        sex_ppv = sex_suspected_and_confirmed / sex_suspected * 100
                        print(f"    Suspected OSA confirmed: {sex_suspected_and_confirmed} ({sex_ppv:.2f}% of suspected cases)")
                        print(f"    Suspected OSA not confirmed: {sex_suspected - sex_suspected_and_confirmed} ({100-sex_ppv:.2f}% of suspected cases)")

            # Chi-square test for sex differences in suspected OSA
            suspected_osa_sex_crosstab = pd.crosstab(df['Sex'], df['has_suspected_osa'])
            print(f"\nSuspected OSA by sex crosstab:")
            print(suspected_osa_sex_crosstab)
            
            if len(suspected_osa_sex_crosstab) > 1 and len(suspected_osa_sex_crosstab.columns) > 1:
                chi2, p, _, _ = chi2_contingency(suspected_osa_sex_crosstab)
                print(f"\nChi-square test for sex differences in suspected OSA: chi2={chi2:.2f}, p={p:.4f}")
                
                if p < 0.05:
                    # Calculate odds ratio for suspected OSA by sex
                    # Assuming männlich=1, weiblich=0 for calculation
                    male_suspected = df[(df['Sex'] == 'männlich') & (df['has_suspected_osa'] == 1)].shape[0]
                    male_not_suspected = df[(df['Sex'] == 'männlich') & (df['has_suspected_osa'] == 0)].shape[0]
                    female_suspected = df[(df['Sex'] == 'weiblich') & (df['has_suspected_osa'] == 1)].shape[0]
                    female_not_suspected = df[(df['Sex'] == 'weiblich') & (df['has_suspected_osa'] == 0)].shape[0]
                    
                    if male_not_suspected > 0 and female_not_suspected > 0:
                        odds_ratio = (male_suspected * female_not_suspected) / (male_not_suspected * female_suspected)
                        print(f"  Odds Ratio (male vs female): {odds_ratio:.2f}")
                        print(f"  Interpretation: Males are {odds_ratio:.2f} times more likely to have suspected OSA than females.")
                    
                    print("  Conclusion: There is a significant sex difference in suspected OSA rates.")
                else:
                    print("  Conclusion: No significant sex difference in suspected OSA rates.")

            # Chi-square test for sex differences in confirmed OSA among suspected cases
            suspected_patients = df[df['has_suspected_osa'] == 1]
            if len(suspected_patients) > 0 and 'Sex' in suspected_patients.columns:
                confirmed_among_suspected_crosstab = pd.crosstab(suspected_patients['Sex'], suspected_patients['has_osa'])
                print(f"\nConfirmed OSA among suspected cases by sex:")
                print(confirmed_among_suspected_crosstab)
                
                if len(confirmed_among_suspected_crosstab) > 1 and len(confirmed_among_suspected_crosstab.columns) > 1:
                    chi2, p, _, _ = chi2_contingency(confirmed_among_suspected_crosstab)
                    print(f"\nChi-square test for sex differences in OSA confirmation among suspected cases: chi2={chi2:.2f}, p={p:.4f}")
                    
                    if p < 0.05:
                        # Calculate odds ratio for confirmation by sex
                        male_confirmed = suspected_patients[(suspected_patients['Sex'] == 'männlich') & (suspected_patients['has_osa'] == 1)].shape[0]
                        male_not_confirmed = suspected_patients[(suspected_patients['Sex'] == 'männlich') & (suspected_patients['has_osa'] == 0)].shape[0]
                        female_confirmed = suspected_patients[(suspected_patients['Sex'] == 'weiblich') & (suspected_patients['has_osa'] == 1)].shape[0]
                        female_not_confirmed = suspected_patients[(suspected_patients['Sex'] == 'weiblich') & (suspected_patients['has_osa'] == 0)].shape[0]
                        
                        if male_not_confirmed > 0 and female_not_confirmed > 0:
                            odds_ratio = (male_confirmed * female_not_confirmed) / (male_not_confirmed * female_confirmed)
                            print(f"  Odds Ratio (male vs female confirmation): {odds_ratio:.2f}")
                            print(f"  Interpretation: Among suspected cases, males are {odds_ratio:.2f} times more likely to have confirmed OSA than females.")
                        
                        print("  Conclusion: There is a significant sex difference in OSA confirmation rates among suspected cases.")
                    else:
                        print("  Conclusion: No significant sex difference in OSA confirmation rates among suspected cases.")

    def _demographic_analysis(self, df):
        """Analyze demographics (sex, age, BMI)."""
        print("\n" + "-"*50)
        print("DEMOGRAPHICS")
        print("-"*50)

        total_patients = len(df)
        # We'll use total_patients for percentage calculations

        # Sex distribution
        if 'Sex' in df.columns and df['Sex'].notna().any():
            sex_distribution = df['Sex'].value_counts()
            print("\nSex distribution in the entire dataset:")
            for sex, count in sex_distribution.items():
                print(f"  {sex}: {count} ({count/total_patients*100:.2f}%)")

            # OSA by sex
            osa_by_sex = df[df['has_osa'] == 1]['Sex'].value_counts()
            print("\nOSA patients by sex:")
            for sex, count in osa_by_sex.items():
                sex_total = sex_distribution[sex]
                print(f"  {sex}: {count} out of {sex_total} ({count/sex_total*100:.2f}%)")

            # Use osa_count for any calculations that need the total number of OSA patients

            # Create a flag for male sex for later use
            df['is_male'] = (df['Sex'] == 'männlich').astype(int)

        # Age analysis - overall
        self._analyze_age(df)

        # BMI analysis
        self._analyze_bmi(df)

        # Sex analysis
        sex_osa = pd.crosstab(df['Sex'], df['has_osa'])
        print("\nSex distribution:")
        print(sex_osa)

        # Chi-square test for sex
        chi2, p, _, _ = chi2_contingency(sex_osa)
        print(f"\nChi-square test for sex: chi2={chi2:.2f}, p={p:.4f}")
        if p < 0.05:
            print("  Conclusion: There is a significant association between sex and OSA.")
        else:
            print("  Conclusion: No significant association between sex and OSA.")

    def _analyze_age(self, df):
        """Analyze age distribution and differences."""
        osa_age = df[df['has_osa'] == 1]['Age'].dropna()
        non_osa_age = df[df['has_osa'] == 0]['Age'].dropna()

        print("\nAge statistics for OSA patients:")
        print(f"  Mean age: {osa_age.mean():.2f} years")
        print(f"  Median age: {osa_age.median():.2f} years")
        print(f"  Age range: {osa_age.min():.2f} - {osa_age.max():.2f} years")

        print("\nAge statistics for non-OSA patients:")
        print(f"  Mean age: {non_osa_age.mean():.2f} years")
        print(f"  Median age: {non_osa_age.median():.2f} years")
        print(f"  Age range: {non_osa_age.min():.2f} - {non_osa_age.max():.2f} years")

        # Age analysis - by sex
        if 'Sex' in df.columns and df['Sex'].notna().any():
            print("\nAge statistics by sex:")
            for sex in df['Sex'].unique():
                if pd.notna(sex):
                    # OSA patients of this sex
                    sex_osa = df[(df['has_osa'] == 1) & (df['Sex'] == sex)]
                    sex_osa_age = sex_osa['Age'].dropna()

                    # Non-OSA patients of this sex
                    sex_non_osa = df[(df['has_osa'] == 0) & (df['Sex'] == sex)]
                    sex_non_osa_age = sex_non_osa['Age'].dropna()

                    print(f"\n  {sex} patients:")
                    if len(sex_osa_age) > 0:
                        print(f"    OSA (n={len(sex_osa_age)}): Mean age {sex_osa_age.mean():.2f}, Median {sex_osa_age.median():.2f}, Range {sex_osa_age.min():.2f}-{sex_osa_age.max():.2f}")
                    else:
                        print("    OSA: No data")

                    if len(sex_non_osa_age) > 0:
                        print(f"    Non-OSA (n={len(sex_non_osa_age)}): Mean age {sex_non_osa_age.mean():.2f}, Median {sex_non_osa_age.median():.2f}, Range {sex_non_osa_age.min():.2f}-{sex_non_osa_age.max():.2f}")
                    else:
                        print("    Non-OSA: No data")

                    # T-test for age difference within this sex
                    if len(sex_osa_age) > 1 and len(sex_non_osa_age) > 1:
                        t_stat, p_val = ttest_ind(sex_osa_age, sex_non_osa_age, equal_var=False)
                        print(f"    T-test for age difference in {sex}: t={t_stat:.2f}, p={p_val:.4f}")
                        if p_val < 0.05:
                            print(f"    Conclusion: Significant age difference between OSA and non-OSA {sex} patients.")
                        else:
                            print(f"    Conclusion: No significant age difference between OSA and non-OSA {sex} patients.")

        # T-test for age - overall
        t_stat, p_val = ttest_ind(osa_age, non_osa_age, equal_var=False)
        print(f"\nT-test for overall age difference: t={t_stat:.2f}, p={p_val:.4f}")
        if p_val < 0.05:
            print("  Conclusion: There is a significant age difference between OSA and non-OSA patients.")
        else:
            print("  Conclusion: No significant age difference between OSA and non-OSA patients.")

    def _analyze_bmi(self, df):
        """Analyze BMI distribution and differences."""
        if 'BMI' in df.columns and df['BMI'].notna().any():
            osa_bmi = df[df['has_osa'] == 1]['BMI'].dropna()
            non_osa_bmi = df[df['has_osa'] == 0]['BMI'].dropna()

            print("\nBMI statistics (overall):")
            print(f"  Mean BMI for OSA patients: {osa_bmi.mean():.2f}")
            print(f"  Mean BMI for non-OSA patients: {non_osa_bmi.mean():.2f}")

            # T-test for BMI
            if len(osa_bmi) > 0 and len(non_osa_bmi) > 0:
                t_stat, p_val = ttest_ind(osa_bmi, non_osa_bmi, equal_var=False)
                print(f"  T-test for BMI difference: t={t_stat:.2f}, p={p_val:.4f}")
                if p_val < 0.05:
                    print("  Conclusion: There is a significant BMI difference between OSA and non-OSA patients.")
                else:
                    print("  Conclusion: No significant BMI difference between OSA and non-OSA patients.")

            # BMI analysis by sex
            if 'Sex' in df.columns and df['Sex'].notna().any():
                print("\nBMI statistics by sex:")
                for sex in df['Sex'].unique():
                    if pd.notna(sex):
                        # OSA patients of this sex
                        sex_osa_bmi = df[(df['has_osa'] == 1) & (df['Sex'] == sex)]['BMI'].dropna()

                        # Non-OSA patients of this sex
                        sex_non_osa_bmi = df[(df['has_osa'] == 0) & (df['Sex'] == sex)]['BMI'].dropna()

                        print(f"\n  {sex} patients:")
                        if len(sex_osa_bmi) > 0:
                            print(f"    OSA (n={len(sex_osa_bmi)}): Mean BMI {sex_osa_bmi.mean():.2f}, Median {sex_osa_bmi.median():.2f}")
                        else:
                            print("    OSA: No BMI data")

                        if len(sex_non_osa_bmi) > 0:
                            print(f"    Non-OSA (n={len(sex_non_osa_bmi)}): Mean BMI {sex_non_osa_bmi.mean():.2f}, Median {sex_non_osa_bmi.median():.2f}")
                        else:
                            print("    Non-OSA: No BMI data")

                        # T-test for BMI difference within this sex
                        if len(sex_osa_bmi) > 1 and len(sex_non_osa_bmi) > 1:
                            t_stat, p_val = ttest_ind(sex_osa_bmi, sex_non_osa_bmi, equal_var=False)
                            print(f"    T-test for BMI difference in {sex}: t={t_stat:.2f}, p={p_val:.4f}")
                            if p_val < 0.05:
                                print(f"    Conclusion: Significant BMI difference between OSA and non-OSA {sex} patients.")
                            else:
                                print(f"    Conclusion: No significant BMI difference between OSA and non-OSA {sex} patients.")

    def _hypertension_analysis(self, df):
        """Analyze hypertension prevalence and association with OSA."""
        print("\n" + "-"*50)
        print("HYPERTENSION ANALYSIS")
        print("-"*50)

        total_patients = len(df)
        osa_patients = df['has_osa'].sum()
        non_osa_patients = total_patients - osa_patients

        # Calculate statistics for different hypertension definitions
        diagnosed_htn = df['has_diagnosed_hypertension'].sum()
        med_implied_htn = df['on_antihypertensives'].sum()
        combined_htn = df['has_hypertension'].sum()

        print("\nHypertension prevalence (overall):")
        print(f"  Diagnosed hypertension (ICD I10-I15): {diagnosed_htn} patients ({diagnosed_htn/total_patients*100:.2f}%)")
        print(f"  On antihypertensive medications: {med_implied_htn} patients ({med_implied_htn/total_patients*100:.2f}%)")
        print(f"  Combined (diagnosed OR on medications): {combined_htn} patients ({combined_htn/total_patients*100:.2f}%)")

        # Hypertension by sex
        if 'Sex' in df.columns and df['Sex'].notna().any():
            print("\nHypertension prevalence by sex:")
            for sex in df['Sex'].unique():
                if pd.notna(sex):
                    sex_patients = df[df['Sex'] == sex]
                    sex_count = len(sex_patients)

                    sex_diagnosed_htn = sex_patients['has_diagnosed_hypertension'].sum()
                    sex_med_htn = sex_patients['on_antihypertensives'].sum()
                    sex_combined_htn = sex_patients['has_hypertension'].sum()

                    print(f"\n  {sex} patients (n={sex_count}):")
                    print(f"    Diagnosed hypertension: {sex_diagnosed_htn} ({sex_diagnosed_htn/sex_count*100:.2f}%)")
                    print(f"    On antihypertensives: {sex_med_htn} ({sex_med_htn/sex_count*100:.2f}%)")
                    print(f"    Combined: {sex_combined_htn} ({sex_combined_htn/sex_count*100:.2f}%)")

        # Hypertension in OSA vs non-OSA - overall
        osa_diagnosed_htn = df[(df['has_osa'] == 1) & (df['has_diagnosed_hypertension'] == 1)].shape[0]
        osa_med_htn = df[(df['has_osa'] == 1) & (df['on_antihypertensives'] == 1)].shape[0]
        osa_combined_htn = df[(df['has_osa'] == 1) & (df['has_hypertension'] == 1)].shape[0]

        print(f"\nHypertension in OSA patients (overall, n={osa_patients}):")
        print(f"  Diagnosed hypertension: {osa_diagnosed_htn} ({osa_diagnosed_htn/osa_patients*100:.2f}%)")
        print(f"  On antihypertensive medications: {osa_med_htn} ({osa_med_htn/osa_patients*100:.2f}%)")
        print(f"  Combined: {osa_combined_htn} ({osa_combined_htn/osa_patients*100:.2f}%)")

        non_osa_diagnosed_htn = df[(df['has_osa'] == 0) & (df['has_diagnosed_hypertension'] == 1)].shape[0]
        non_osa_med_htn = df[(df['has_osa'] == 0) & (df['on_antihypertensives'] == 1)].shape[0]
        non_osa_combined_htn = df[(df['has_osa'] == 0) & (df['has_hypertension'] == 1)].shape[0]
        print(f"\nHypertension in non-OSA patients (overall, n={non_osa_patients}):")
        print(f"  Diagnosed hypertension: {non_osa_diagnosed_htn} ({non_osa_diagnosed_htn/non_osa_patients*100:.2f}%)")
        print(f"  On antihypertensive medications: {non_osa_med_htn} ({non_osa_med_htn/non_osa_patients*100:.2f}%)")
        print(f"  Combined: {non_osa_combined_htn} ({non_osa_combined_htn/non_osa_patients*100:.2f}%)")

        # Hypertension in OSA vs non-OSA - by sex
        self._analyze_hypertension_by_sex(df)

        # Chi-square test for combined hypertension - overall
        htn_contingency = pd.crosstab(df['has_osa'], df['has_hypertension'])
        chi2, p, _, _ = chi2_contingency(htn_contingency)

        print(f"\nChi-square test for association between OSA and hypertension (overall): chi2={chi2:.2f}, p={p:.4f}")
        if p < 0.05:
            # Calculate odds ratio
            a = htn_contingency.loc[1, 1] if 1 in htn_contingency.index and 1 in htn_contingency.columns else 0
            b = htn_contingency.loc[1, 0] if 1 in htn_contingency.index and 0 in htn_contingency.columns else 0
            c = htn_contingency.loc[0, 1] if 0 in htn_contingency.index and 1 in htn_contingency.columns else 0
            d = htn_contingency.loc[0, 0] if 0 in htn_contingency.index and 0 in htn_contingency.columns else 0

            odds_ratio = (a * d) / (b * c) if b > 0 and c > 0 else float('inf')
            print(f"  Odds Ratio: {odds_ratio:.2f}")
            print(f"  Interpretation: OSA patients are {odds_ratio:.2f} times more likely to have hypertension than non-OSA patients.")
            print("  Conclusion: There is a significant association between OSA and hypertension.")
        else:
            print("  Conclusion: No significant association between OSA and hypertension.")

    def _analyze_hypertension_by_sex(self, df):
        """Analyze hypertension by sex."""
        if 'Sex' in df.columns and df['Sex'].notna().any():
            print("\nHypertension in OSA vs non-OSA patients by sex:")
            for sex in df['Sex'].unique():
                if pd.notna(sex):
                    # Count patients of this sex
                    sex_osa_patients = df[(df['has_osa'] == 1) & (df['Sex'] == sex)].shape[0]
                    sex_non_osa_patients = df[(df['has_osa'] == 0) & (df['Sex'] == sex)].shape[0]

                    if sex_osa_patients > 0:
                        # OSA patients of this sex with hypertension
                        sex_osa_diagnosed_htn = df[(df['has_osa'] == 1) & (df['Sex'] == sex) & (df['has_diagnosed_hypertension'] == 1)].shape[0]
                        sex_osa_med_htn = df[(df['has_osa'] == 1) & (df['Sex'] == sex) & (df['on_antihypertensives'] == 1)].shape[0]
                        sex_osa_combined_htn = df[(df['has_osa'] == 1) & (df['Sex'] == sex) & (df['has_hypertension'] == 1)].shape[0]

                        print(f"\n  {sex} OSA patients (n={sex_osa_patients}):")
                        print(f"    Diagnosed hypertension: {sex_osa_diagnosed_htn} ({sex_osa_diagnosed_htn/sex_osa_patients*100:.2f}%)")
                        print(f"    On antihypertensives: {sex_osa_med_htn} ({sex_osa_med_htn/sex_osa_patients*100:.2f}%)")
                        print(f"    Combined: {sex_osa_combined_htn} ({sex_osa_combined_htn/sex_osa_patients*100:.2f}%)")

                    if sex_non_osa_patients > 0:
                        # Non-OSA patients of this sex with hypertension
                        sex_non_osa_diagnosed_htn = df[(df['has_osa'] == 0) & (df['Sex'] == sex) & (df['has_diagnosed_hypertension'] == 1)].shape[0]
                        sex_non_osa_med_htn = df[(df['has_osa'] == 0) & (df['Sex'] == sex) & (df['on_antihypertensives'] == 1)].shape[0]
                        sex_non_osa_combined_htn = df[(df['has_osa'] == 0) & (df['Sex'] == sex) & (df['has_hypertension'] == 1)].shape[0]

                        print(f"\n  {sex} non-OSA patients (n={sex_non_osa_patients}):")
                        print(f"    Diagnosed hypertension: {sex_non_osa_diagnosed_htn} ({sex_non_osa_diagnosed_htn/sex_non_osa_patients*100:.2f}%)")
                        print(f"    On antihypertensives: {sex_non_osa_med_htn} ({sex_non_osa_med_htn/sex_non_osa_patients*100:.2f}%)")
                        print(f"    Combined: {sex_non_osa_combined_htn} ({sex_non_osa_combined_htn/sex_non_osa_patients*100:.2f}%)")

                    # Chi-square test for hypertension by OSA status within this sex
                    if sex_osa_patients > 0 and sex_non_osa_patients > 0:
                        sex_htn_contingency = pd.crosstab(
                            df[df['Sex'] == sex]['has_osa'],
                            df[df['Sex'] == sex]['has_hypertension']
                        )

                        if len(sex_htn_contingency) > 1 and len(sex_htn_contingency.columns) > 1:
                            chi2, p, _, _ = chi2_contingency(sex_htn_contingency)
                            print(f"\n  Chi-square test for hypertension in {sex} patients: chi2={chi2:.2f}, p={p:.4f}")

                            if p < 0.05:
                                # Calculate odds ratio
                                a = sex_htn_contingency.loc[1, 1] if 1 in sex_htn_contingency.index and 1 in sex_htn_contingency.columns else 0
                                b = sex_htn_contingency.loc[1, 0] if 1 in sex_htn_contingency.index and 0 in sex_htn_contingency.columns else 0
                                c = sex_htn_contingency.loc[0, 1] if 0 in sex_htn_contingency.index and 1 in sex_htn_contingency.columns else 0
                                d = sex_htn_contingency.loc[0, 0] if 0 in sex_htn_contingency.index and 0 in sex_htn_contingency.columns else 0

                                if b > 0 and c > 0:
                                    odds_ratio = (a * d) / (b * c)
                                    print(f"  Odds Ratio: {odds_ratio:.2f}")
                                    print(f"  Interpretation: {sex} OSA patients are {odds_ratio:.2f} times more likely to have hypertension than {sex} non-OSA patients.")

                                print(f"  Conclusion: Significant association between OSA and hypertension in {sex} patients.")
                            else:
                                print(f"  Conclusion: No significant association between OSA and hypertension in {sex} patients.")

    def _medication_analysis(self, df, atc_classes):
        """Analyze antihypertensive medication use."""
        print("\n" + "-"*50)
        print("ANTIHYPERTENSIVE MEDICATION CLASSES")
        print("-"*50)

        osa_patients = df['has_osa'].sum()
        non_osa_patients = len(df) - osa_patients

        for atc_prefix, name in atc_classes.items():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in df.columns:
                # Overall statistics
                osa_on_med = df[(df['has_osa'] == 1) & (df[col_name] == 1)].shape[0]
                non_osa_on_med = df[(df['has_osa'] == 0) & (df[col_name] == 1)].shape[0]

                osa_pct = osa_on_med / osa_patients * 100 if osa_patients > 0 else 0
                non_osa_pct = non_osa_on_med / non_osa_patients * 100 if non_osa_patients > 0 else 0

                print(f"\n{name} (ATC {atc_prefix}) - Overall:")
                print(f"  OSA patients: {osa_on_med} ({osa_pct:.2f}%)")
                print(f"  Non-OSA patients: {non_osa_on_med} ({non_osa_pct:.2f}%)")

                # Chi-square test - overall
                med_contingency = pd.crosstab(df['has_osa'], df[col_name])
                chi2, p, _, _ = chi2_contingency(med_contingency)

                print(f"  Chi-square test: chi2={chi2:.2f}, p={p:.4f}")
                if p < 0.05:
                    # Calculate odds ratio
                    a = med_contingency.loc[1, 1] if 1 in med_contingency.index and 1 in med_contingency.columns else 0
                    b = med_contingency.loc[1, 0] if 1 in med_contingency.index and 0 in med_contingency.columns else 0
                    c = med_contingency.loc[0, 1] if 0 in med_contingency.index and 1 in med_contingency.columns else 0
                    d = med_contingency.loc[0, 0] if 0 in med_contingency.index and 0 in med_contingency.columns else 0

                    odds_ratio = (a * d) / (b * c) if b > 0 and c > 0 else float('inf')
                    print(f"  Odds Ratio: {odds_ratio:.2f}")
                    print(f"  Conclusion: There is a significant association between OSA and {name} use.")
                else:
                    print(f"  Conclusion: No significant association between OSA and {name} use.")

                # By sex
                if 'Sex' in df.columns and df['Sex'].notna().any():
                    print(f"\n  {name} use by sex:")
                    for sex in df['Sex'].unique():
                        if pd.notna(sex):
                            # Count patients of this sex
                            sex_osa_patients = df[(df['has_osa'] == 1) & (df['Sex'] == sex)].shape[0]
                            sex_non_osa_patients = df[(df['has_osa'] == 0) & (df['Sex'] == sex)].shape[0]

                            if sex_osa_patients > 0 and sex_non_osa_patients > 0:
                                # Medication use in this sex
                                sex_osa_on_med = df[(df['has_osa'] == 1) & (df['Sex'] == sex) & (df[col_name] == 1)].shape[0]
                                sex_non_osa_on_med = df[(df['has_osa'] == 0) & (df['Sex'] == sex) & (df[col_name] == 1)].shape[0]

                                sex_osa_pct = sex_osa_on_med / sex_osa_patients * 100
                                sex_non_osa_pct = sex_non_osa_on_med / sex_non_osa_patients * 100

                                print(f"\n    {sex} patients:")
                                print(f"      OSA (n={sex_osa_patients}): {sex_osa_on_med} ({sex_osa_pct:.2f}%)")
                                print(f"      Non-OSA (n={sex_non_osa_patients}): {sex_non_osa_on_med} ({sex_non_osa_pct:.2f}%)")

                                # Chi-square test for this sex
                                sex_med_contingency = pd.crosstab(
                                    df[df['Sex'] == sex]['has_osa'],
                                    df[df['Sex'] == sex][col_name]
                                )

                                if len(sex_med_contingency) > 1 and len(sex_med_contingency.columns) > 1:
                                    chi2, p, _, _ = chi2_contingency(sex_med_contingency)
                                    print(f"      Chi-square test: chi2={chi2:.2f}, p={p:.4f}")

                                    if p < 0.05:
                                        # Calculate odds ratio
                                        a = sex_med_contingency.loc[1, 1] if 1 in sex_med_contingency.index and 1 in sex_med_contingency.columns else 0
                                        b = sex_med_contingency.loc[1, 0] if 1 in sex_med_contingency.index and 0 in sex_med_contingency.columns else 0
                                        c = sex_med_contingency.loc[0, 1] if 0 in sex_med_contingency.index and 1 in sex_med_contingency.columns else 0
                                        d = sex_med_contingency.loc[0, 0] if 0 in sex_med_contingency.index and 0 in sex_med_contingency.columns else 0

                                        if b > 0 and c > 0:
                                            odds_ratio = (a * d) / (b * c)
                                            print(f"      Odds Ratio: {odds_ratio:.2f}")
                                            print(f"      Interpretation: {sex} OSA patients are {odds_ratio:.2f} times more likely to use {name} than {sex} non-OSA patients.")

                                        print(f"      Conclusion: Significant association between OSA and {name} use in {sex} patients.")
                                    else:
                                        print(f"      Conclusion: No significant association between OSA and {name} use in {sex} patients.")

    def _comorbidity_analysis(self, df):
        """Analyze comorbidities and their association with OSA."""
        print("\n" + "-"*50)
        print("OTHER COMORBIDITIES")
        print("-"*50)

        comorbidities = [
            ('Diabetes', 'has_diabetes'),
            ('Cardiovascular Disease', 'has_cardiovascular')
        ]

        for name, col in comorbidities:
            # Create contingency table - overall
            contingency = pd.crosstab(df['has_osa'], df[col])

            # Calculate percentages - overall
            osa_with_condition = contingency.loc[1, 1] if 1 in contingency.index and 1 in contingency.columns else 0
            osa_total = df['has_osa'].sum()
            non_osa_with_condition = contingency.loc[0, 1] if 0 in contingency.index and 1 in contingency.columns else 0
            non_osa_total = (df['has_osa'] == 0).sum()

            osa_pct = (osa_with_condition / osa_total * 100) if osa_total > 0 else 0
            non_osa_pct = (non_osa_with_condition / non_osa_total * 100) if non_osa_total > 0 else 0

            print(f"\n{name} - Overall:")
            print(f"  OSA patients with {name}: {osa_with_condition} out of {osa_total} ({osa_pct:.2f}%)")
            print(f"  Non-OSA patients with {name}: {non_osa_with_condition} out of {non_osa_total} ({non_osa_pct:.2f}%)")

            # Chi-square test - overall
            if 1 in contingency.index and 0 in contingency.index and 1 in contingency.columns and 0 in contingency.columns:
                chi2, p, _, _ = chi2_contingency(contingency)
                print(f"  Chi-square test: chi2={chi2:.2f}, p={p:.4f}")

                # Calculate odds ratio
                a = contingency.loc[1, 1] if 1 in contingency.index and 1 in contingency.columns else 0
                b = contingency.loc[1, 0] if 1 in contingency.index and 0 in contingency.columns else 0
                c = contingency.loc[0, 1] if 0 in contingency.index and 1 in contingency.columns else 0
                d = contingency.loc[0, 0] if 0 in contingency.index and 0 in contingency.columns else 0

                # Avoid division by zero
                if b > 0 and c > 0:
                    odds_ratio = (a * d) / (b * c)
                    print(f"  Odds Ratio: {odds_ratio:.2f}")
                    print(f"  Interpretation: OSA patients are {odds_ratio:.2f} times more likely to have {name} than non-OSA patients.")

                if p < 0.05:
                    print(f"  Conclusion: There is a significant association between OSA and {name}.")
                else:
                    print(f"  Conclusion: No significant association between OSA and {name}.")

            # By sex
            if 'Sex' in df.columns and df['Sex'].notna().any():
                print(f"\n  {name} by sex:")
                for sex in df['Sex'].unique():
                    if pd.notna(sex):
                        # Count patients of this sex
                        sex_osa_patients = df[(df['has_osa'] == 1) & (df['Sex'] == sex)].shape[0]
                        sex_non_osa_patients = df[(df['has_osa'] == 0) & (df['Sex'] == sex)].shape[0]

                        if sex_osa_patients > 0 and sex_non_osa_patients > 0:
                            # Condition in this sex
                            sex_osa_with_condition = df[(df['has_osa'] == 1) & (df['Sex'] == sex) & (df[col] == 1)].shape[0]
                            sex_non_osa_with_condition = df[(df['has_osa'] == 0) & (df['Sex'] == sex) & (df[col] == 1)].shape[0]

                            sex_osa_pct = sex_osa_with_condition / sex_osa_patients * 100
                            sex_non_osa_pct = sex_non_osa_with_condition / sex_non_osa_patients * 100

                            print(f"\n    {sex} patients:")
                            print(f"      OSA (n={sex_osa_patients}): {sex_osa_with_condition} ({sex_osa_pct:.2f}%)")
                            print(f"      Non-OSA (n={sex_non_osa_patients}): {sex_non_osa_with_condition} ({sex_non_osa_pct:.2f}%)")

                            # Chi-square test for this sex
                            sex_condition_contingency = pd.crosstab(
                                df[df['Sex'] == sex]['has_osa'],
                                df[df['Sex'] == sex][col]
                            )

                            if len(sex_condition_contingency) > 1 and len(sex_condition_contingency.columns) > 1:
                                chi2, p, _, _ = chi2_contingency(sex_condition_contingency)
                                print(f"      Chi-square test: chi2={chi2:.2f}, p={p:.4f}")

                                if p < 0.05:
                                    # Calculate odds ratio
                                    a = sex_condition_contingency.loc[1, 1] if 1 in sex_condition_contingency.index and 1 in sex_condition_contingency.columns else 0
                                    b = sex_condition_contingency.loc[1, 0] if 1 in sex_condition_contingency.index and 0 in sex_condition_contingency.columns else 0
                                    c = sex_condition_contingency.loc[0, 1] if 0 in sex_condition_contingency.index and 1 in sex_condition_contingency.columns else 0
                                    d = sex_condition_contingency.loc[0, 0] if 0 in sex_condition_contingency.index and 0 in sex_condition_contingency.columns else 0

                                    if b > 0 and c > 0:
                                        odds_ratio = (a * d) / (b * c)
                                        print(f"      Odds Ratio: {odds_ratio:.2f}")
                                        print(f"      Interpretation: {sex} OSA patients are {odds_ratio:.2f} times more likely to have {name} than {sex} non-OSA patients.")

                                    print(f"      Conclusion: Significant association between OSA and {name} in {sex} patients.")
                                else:
                                    print(f"      Conclusion: No significant association between OSA and {name} in {sex} patients.")

    def _diagnosis_analysis(self, df):
        """Analyze sleep-related and non-sleep-related diagnoses."""
        print("\n" + "-"*50)
        print("DIAGNOSIS OVERVIEW")
        print("-"*50)

        # Sleep-related diagnoses
        print("\nSleep-Related Diagnoses:")
        sleep_diagnoses = [
            ('Obstructive Sleep Apnea', 'has_osa'),
            ('Insomnia', 'has_insomnia'),
            ('Narcolepsy', 'has_narcolepsy'),
            ('Restless Legs Syndrome', 'has_rls'),
            ('Sleep-Related Breathing Disorders', 'has_sleep_breathing_disorders')
        ]

        # Check which columns actually exist in the dataframe
        sleep_diagnoses = [(name, col) for name, col in sleep_diagnoses if col in df.columns]

        # Analyze sleep-related diagnoses
        for name, col in sleep_diagnoses:
            if col in df.columns:
                count = df[col].sum()
                percentage = count / len(df) * 100
                print(f"  {name}: {count} patients ({percentage:.2f}%)")

                # Breakdown by sex if available
                if 'Sex' in df.columns and df['Sex'].notna().any():
                    print("    By sex:")
                    for sex in df['Sex'].unique():
                        if pd.notna(sex):
                            sex_count = df[(df[col] == 1) & (df['Sex'] == sex)].shape[0]
                            sex_total = df[df['Sex'] == sex].shape[0]
                            sex_percentage = sex_count / sex_total * 100 if sex_total > 0 else 0
                            print(f"      {sex}: {sex_count} out of {sex_total} ({sex_percentage:.2f}%)")

        # Non-sleep-related diagnoses
        print("\nNon-Sleep-Related Diagnoses:")
        non_sleep_diagnoses = [
            ('Hypertension', 'has_diagnosed_hypertension'),
            ('Diabetes', 'has_diabetes'),
            ('Cardiovascular Disease', 'has_cardiovascular'),
            ('Depression', 'has_depression'),
            ('Anxiety', 'has_anxiety'),
            ('COPD', 'has_copd')
        ]

        # Check which columns actually exist in the dataframe
        non_sleep_diagnoses = [(name, col) for name, col in non_sleep_diagnoses if col in df.columns]

        # Analyze non-sleep-related diagnoses
        for name, col in non_sleep_diagnoses:
            if col in df.columns:
                count = df[col].sum()
                percentage = count / len(df) * 100
                print(f"  {name}: {count} patients ({percentage:.2f}%)")

                # Breakdown by OSA status
                osa_with_condition = df[(df['has_osa'] == 1) & (df[col] == 1)].shape[0]
                osa_total = df['has_osa'].sum()
                osa_percentage = osa_with_condition / osa_total * 100 if osa_total > 0 else 0

                non_osa_with_condition = df[(df['has_osa'] == 0) & (df[col] == 1)].shape[0]
                non_osa_total = (df['has_osa'] == 0).sum()
                non_osa_percentage = non_osa_with_condition / non_osa_total * 100 if non_osa_total > 0 else 0

                print(f"    OSA patients: {osa_with_condition} out of {osa_total} ({osa_percentage:.2f}%)")
                print(f"    Non-OSA patients: {non_osa_with_condition} out of {non_osa_total} ({non_osa_percentage:.2f}%)")

                # Chi-square test if possible
                contingency = pd.crosstab(df['has_osa'], df[col])
                if len(contingency) > 1 and len(contingency.columns) > 1:
                    chi2, p, _, _ = chi2_contingency(contingency)
                    print(f"    Chi-square test: chi2={chi2:.2f}, p={p:.4f}")

                    if p < 0.05:
                        # Calculate odds ratio
                        a = contingency.loc[1, 1] if 1 in contingency.index and 1 in contingency.columns else 0
                        b = contingency.loc[1, 0] if 1 in contingency.index and 0 in contingency.columns else 0
                        c = contingency.loc[0, 1] if 0 in contingency.index and 1 in contingency.columns else 0
                        d = contingency.loc[0, 0] if 0 in contingency.index and 0 in contingency.columns else 0

                        if b > 0 and c > 0:
                            odds_ratio = (a * d) / (b * c)
                            print(f"    Odds Ratio: {odds_ratio:.2f}")
                            print(f"    Interpretation: OSA patients are {odds_ratio:.2f} times more likely to have {name} than non-OSA patients.")

                        print(f"    Conclusion: Significant association between OSA and {name}.")
                    else:
                        print(f"    Conclusion: No significant association between OSA and {name}.")

    def _correlation_analysis(self, df):
        """Analyze correlations between BMI and age with visualizations."""
        print("\n" + "-"*50)
        print("CORRELATION ANALYSIS: BMI AND AGE")
        print("-"*50)

        # Check if BMI and Age columns exist and have data
        if 'BMI' not in df.columns or 'Age' not in df.columns:
            print("\nCannot perform correlation analysis: BMI or Age data missing.")
            return

        # Create a directory for saving plots if it doesn't exist
        os.makedirs('plots', exist_ok=True)

        # Filter out rows with missing BMI or Age
        df_filtered = df.dropna(subset=['BMI', 'Age'])

        if len(df_filtered) < 2:
            print("\nInsufficient data for correlation analysis after removing missing values.")
            return

        # Calculate overall correlation
        corr, p_value = pearsonr(df_filtered['BMI'], df_filtered['Age'])
        print("\nOverall correlation between BMI and Age:")
        print(f"  Pearson correlation coefficient: {corr:.4f}")
        print(f"  p-value: {p_value:.4f}")

        if p_value < 0.05:
            print("  Conclusion: There is a significant correlation between BMI and Age (p < 0.05).")
            if corr > 0:
                print("  The correlation is positive, suggesting BMI tends to increase with age.")
            else:
                print("  The correlation is negative, suggesting BMI tends to decrease with age.")
        else:
            print("  Conclusion: No significant correlation between BMI and Age (p >= 0.05).")

        # Correlation by OSA status
        print("\nCorrelation between BMI and Age by OSA status:")

        # OSA patients
        osa_df = df_filtered[df_filtered['has_osa'] == 1]
        if len(osa_df) >= 2:
            osa_corr, osa_p = pearsonr(osa_df['BMI'], osa_df['Age'])
            print(f"  OSA patients (n={len(osa_df)}):")
            print(f"    Correlation: {osa_corr:.4f}, p-value: {osa_p:.4f}")
            if osa_p < 0.05:
                print("    Conclusion: Significant correlation in OSA patients (p < 0.05).")
            else:
                print("    Conclusion: No significant correlation in OSA patients (p >= 0.05).")
        else:
            print("  Insufficient OSA patients with complete data for correlation analysis.")

        # Non-OSA patients
        non_osa_df = df_filtered[df_filtered['has_osa'] == 0]
        if len(non_osa_df) >= 2:
            non_osa_corr, non_osa_p = pearsonr(non_osa_df['BMI'], non_osa_df['Age'])
            print(f"  Non-OSA patients (n={len(non_osa_df)}):")
            print(f"    Correlation: {non_osa_corr:.4f}, p-value: {non_osa_p:.4f}")
            if non_osa_p < 0.05:
                print("    Conclusion: Significant correlation in non-OSA patients (p < 0.05).")
            else:
                print("    Conclusion: No significant correlation in non-OSA patients (p >= 0.05).")
        else:
            print("  Insufficient non-OSA patients with complete data for correlation analysis.")

        # Generate scatter plot with regression line for overall data
        plt.figure(figsize=(10, 6))
        sns.set_style("whitegrid")

        # Create scatter plot with regression line
        sns.regplot(x='Age', y='BMI', data=df_filtered, scatter_kws={'alpha':0.5}, line_kws={'color':'red'})

        plt.title('Correlation between Age and BMI', fontsize=16)
        plt.xlabel('Age (years)', fontsize=12)
        plt.ylabel('BMI', fontsize=12)
        plt.grid(True)

        # Add correlation information to the plot
        plt.annotate(f'Correlation: {corr:.4f}\np-value: {p_value:.4f}',
                    xy=(0.05, 0.95), xycoords='axes fraction',
                    bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

        # Save the plot
        plt.tight_layout()
        plt.savefig('plots/bmi_age_correlation.png', dpi=300)
        print("\nScatter plot saved to 'plots/bmi_age_correlation.png'")
        plt.close()

        # Generate scatter plot with regression lines by OSA status
        plt.figure(figsize=(10, 6))

        # Plot points and regression lines by OSA status
        sns.scatterplot(x='Age', y='BMI', hue='has_osa', data=df_filtered,
                       palette={0:'blue', 1:'red'}, alpha=0.5)

        if len(osa_df) >= 2:
            sns.regplot(x='Age', y='BMI', data=osa_df, scatter=False,
                      line_kws={'color':'red', 'label':f'OSA (r={osa_corr:.2f})'})

        if len(non_osa_df) >= 2:
            sns.regplot(x='Age', y='BMI', data=non_osa_df, scatter=False,
                      line_kws={'color':'blue', 'label':f'Non-OSA (r={non_osa_corr:.2f})'})

        plt.title('Correlation between Age and BMI by OSA Status', fontsize=16)
        plt.xlabel('Age (years)', fontsize=12)
        plt.ylabel('BMI', fontsize=12)
        plt.legend(title='Patient Group')
        plt.grid(True)

        # Save the plot
        plt.tight_layout()
        plt.savefig('plots/bmi_age_correlation_by_osa.png', dpi=300)
        print("Scatter plot by OSA status saved to 'plots/bmi_age_correlation_by_osa.png'")
        plt.close()

        # If sex data is available, analyze by sex
        if 'Sex' in df.columns and df['Sex'].notna().any():
            print("\nCorrelation between BMI and Age by sex:")

            for sex in df['Sex'].unique():
                if pd.notna(sex):
                    sex_df = df_filtered[df_filtered['Sex'] == sex]

                    if len(sex_df) >= 2:
                        sex_corr, sex_p = pearsonr(sex_df['BMI'], sex_df['Age'])
                        print(f"  {sex} patients (n={len(sex_df)}):")
                        print(f"    Correlation: {sex_corr:.4f}, p-value: {sex_p:.4f}")

                        if sex_p < 0.05:
                            print(f"    Conclusion: Significant correlation in {sex} patients (p < 0.05).")
                        else:
                            print(f"    Conclusion: No significant correlation in {sex} patients (p >= 0.05).")

                        # Generate scatter plot for this sex
                        plt.figure(figsize=(10, 6))
                        sns.regplot(x='Age', y='BMI', data=sex_df, scatter_kws={'alpha':0.5}, line_kws={'color':'purple'})

                        plt.title(f'Correlation between Age and BMI for {sex} Patients', fontsize=16)
                        plt.xlabel('Age (years)', fontsize=12)
                        plt.ylabel('BMI', fontsize=12)
                        plt.grid(True)

                        # Add correlation information to the plot
                        plt.annotate(f'Correlation: {sex_corr:.4f}\np-value: {sex_p:.4f}',
                                    xy=(0.05, 0.95), xycoords='axes fraction',
                                    bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

                        # Save the plot
                        plt.tight_layout()
                        plt.savefig(f'plots/bmi_age_correlation_{sex}.png', dpi=300)
                        print(f"Scatter plot for {sex} patients saved to 'plots/bmi_age_correlation_{sex}.png'")
                        plt.close()
                    else:
                        print(f"  Insufficient {sex} patients with complete data for correlation analysis.")

    def _medication_patterns(self, top_osa_meds, top_osa_atc):
        """Analyze medication patterns for OSA patients."""
        print("\n" + "-"*50)
        print("MEDICATION PATTERNS")
        print("-"*50)

        print("\nTop 10 medications for OSA patients:")
        for med, count in top_osa_meds.items():
            print(f"  {med}: {count}")

        print("\nTop 10 medication classes (ATC codes) for OSA patients:")
        for atc, count in top_osa_atc.items():
            print(f"  {atc}: {count}")
